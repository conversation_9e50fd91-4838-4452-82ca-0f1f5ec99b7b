<!-- about.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>About | Nopole Flairan Favour</title>
  <link rel="stylesheet" href="style.css" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <style>
    .about-section {
      padding: 80px 0;
      background: linear-gradient(to right, #f8f9fa, #e9ecef);
    }
    .about-content {
      max-width: 850px;
      margin: auto;
      background: white;
      border-radius: 16px;
      padding: 40px;
      box-shadow: 0 5px 30px rgba(0, 0, 0, 0.05);
    }
    .highlight {
      color: #0d6efd;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="index.html">Nopole Flairan Favour</a>
      <button class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
          <li class="nav-item"><a class="nav-link active" href="about.html">About</a></li>
          <li class="nav-item"><a class="nav-link" href="services.html">Services</a></li>
          <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
          <li class="nav-item"><a class="nav-link" href="project.html">Projects</a></li>

        </ul>
      </div>
    </div>
  </nav>

  <!-- About Section -->
  <section class="about-section">
    <div class="container">
      <div class="about-content">
        <h2 class="text-center mb-4">About Me</h2>
        <p class="lead">
          I strive to create <span class="highlight">web applications</span> that not only meet client goals but also delight users.
          With a strong attention to detail and a continuous drive to stay updated with the latest trends in technology, 
          I aim to build experiences that are seamless, responsive, and impactful.
        </p>
        <p>
          I’m currently pursuing a <span class="highlight">Bachelor's degree in Software Engineering</span> and have actively sought out 
          opportunities to grow both professionally and personally. I’ve interned at tech companies, 
          where I gained real-world experience in development and system design.
        </p>
        <p>
          As part of my journey, I’ve participated in over <span class="highlight">3+ hackathons</span>, not only for competition but 
          for the fun of learning and collaborating with other passionate developers. I also had the privilege of being part of the 
          <span class="highlight">first edition of the Cameroon International Tech Summit (CITS) 2024</span>, which was an eye-opening 
          experience that broadened my understanding of global tech trends and innovation in Africa.
        </p>
        <p>
          I believe in <span class="highlight">learning by building</span>, and I’m always on the lookout for new challenges and 
          collaborations that can help me grow while making a difference.
        </p>
      </div>
    </div>
  </section>

  <footer class="text-center py-3 bg-dark text-white">© 2025 Nopole Flairan Favour</footer>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
