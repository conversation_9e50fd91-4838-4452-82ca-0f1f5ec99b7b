<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Projects | Nopole Flairan Favour</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <!-- AOS Animation CSS -->
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet" />

  <style>
    body {
      font-family: "Segoe UI", sans-serif;
      background-color: #f8f9fa;
    }

    .projects-section {
      padding: 80px 0;
    }

    .project-card {
      border-radius: 16px;
      box-shadow: 0 4px 25px rgba(0, 0, 0, 0.06);
      transition: transform 0.3s;
    }

    .project-card:hover {
      transform: translateY(-5px);
    }

    .project-title {
      color: #0d6efd;
      font-weight: 600;
    }

    .project-description {
      font-size: 0.95rem;
    }

    footer {
      background-color: #212529;
      color: #fff;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="index.html">Nopole Flairan Favour</a>
      <button class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
          <li class="nav-item"><a class="nav-link" href="about.html">About</a></li>
          <li class="nav-item"><a class="nav-link" href="services.html">Services</a></li>
          <li class="nav-item"><a class="nav-link active" href="project.html">Projects</a></li>
          <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Projects Section -->
  <section class="projects-section">
    <div class="container">
      <h2 class="text-center mb-5">My Projects</h2>
      <div class="row g-4">

        <!-- Project 1 -->
        <div class="col-md-6 col-lg-4" data-aos="fade-up">
          <div class="card project-card p-3">
            <div class="card-body">
              <h5 class="project-title">AI Job Matching System</h5>
              <p class="project-description">
                A resume-parsing AI system that intelligently matches job seekers with suitable job roles. Built with Python and Hugging Face Transformers.
              </p>
              <div class="d-flex justify-content-between mt-3">
                <a href="#" class="btn btn-outline-primary btn-sm">View Project</a>
                <a href="https://github.com/Favourez/localized-job-finder-using-ai" target="_blank" class="btn btn-dark btn-sm">
                  <i class="bi bi-github"></i> GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 2 -->
        <div class="col-md-6 col-lg-4" data-aos="zoom-in">
          <div class="card project-card p-3">
            <div class="card-body">
              <h5 class="project-title">Translation App (English to Bulu)</h5>
              <p class="project-description">
                A low-resource translation web app for Cameroonian languages using T5-small model and a custom Bulu tokenizer.
              </p>
              <div class="d-flex justify-content-between mt-3">
                <a href="#" class="btn btn-outline-primary btn-sm">View Project</a>
                <a href="https://github.com/Favourez/French-to-Ewondo-Translator" target="_blank" class="btn btn-dark btn-sm">
                  <i class="bi bi-github"></i> GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 3 -->
        <div class="col-md-6 col-lg-4" data-aos="fade-right">
          <div class="card project-card p-3">
            <div class="card-body">
              <h5 class="project-title">Hospital Management System</h5>
              <p class="project-description">
                A web-based PHP/MySQL system for managing hospital data including appointments, patients, and billing.
              </p>
              <div class="d-flex justify-content-between mt-3">
                <a href="#" class="btn btn-outline-primary btn-sm">View Project</a>
                <a href="https://github.com/yourusername/hospital-management" target="_blank" class="btn btn-dark btn-sm">
                  <i class="bi bi-github"></i> GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 4 -->
        <div class="col-md-6 col-lg-4" data-aos="flip-left">
          <div class="card project-card p-3">
            <div class="card-body">
              <h5 class="project-title">Restaurant Ordering System</h5>
              <p class="project-description">
                An interactive restaurant web app using HTML, CSS, JS, and Firebase to manage orders in real time.
              </p>
              <div class="d-flex justify-content-between mt-3">
                <a href="restaurant.html" class="btn btn-outline-primary btn-sm">View Project</a>
                <a href="https://github.com/Favourez/FOODCAM" target="_blank" class="btn btn-dark btn-sm">
                  <i class="bi bi-github"></i> GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Project 5 -->
        <div class="col-md-6 col-lg-4" data-aos="fade-left">
          <div class="card project-card p-3">
            <div class="card-body">
              <h5 class="project-title">Inventory Management System</h5>
              <p class="project-description">
                A desktop inventory application built in Java with database integration for CRUD operations and real-time stock tracking.
              </p>
              <div class="d-flex justify-content-between mt-3">
                <a href="#" class="btn btn-outline-primary btn-sm">View Project</a>
                <a href="https://github.com/Favourez/invetory_managemant_system_with_java" target="_blank" class="btn btn-dark btn-sm">
                  <i class="bi bi-github"></i> GitHub
                </a>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="text-center py-3">
    © 2025 Nopole Flairan Favour
  </footer>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <!-- AOS JS -->
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>
</body>
</html>
